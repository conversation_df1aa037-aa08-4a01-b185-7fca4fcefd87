import "./Calendar.css";
import React, { useState, useEffect } from "react";

// 日历组件
const OutlookCalendar = ({
  date = new Date(),
  events = [],
  mode = "year", // 默认显示月视图
  renderDayContent = (date, events) => null,
  renderWeekdayHeader = weekday => weekday,
  renderWeekdayHeaderExtra = () => null,
  renderMonthHeader = (month, year) => `${month} ${year}`,
  renderMonthHeaderExtra = () => null, // 新增：月份头部额外内容
  renderMonthContent = (month, year, events) => null, // 新增：月份内容渲染
  firstDayOfWeek = 0, // 0 = 星期日, 1 = 星期一
  showOtherMonths = true, // 是否显示非当前月的日期
}) => {
  // 状态管理
  const [currentDate, setCurrentDate] = useState(date);
  const [today, setToday] = useState(new Date());
  // const [mode, setmode] = useState(mode);

  // 监听 date prop 的变化
  useEffect(() => {
    setCurrentDate(date);
  }, [date]);

  // 获取当前年月
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  // 月份名称数组
  const monthNames = [
    { 一月: "01" },
    { 二月: "02" },
    { 三月: "03" },
    { 四月: "04" },
    { 五月: "05" },
    { 六月: "06" },
    { 七月: "07" },
    { 八月: "08" },
    { 九月: "09" },
    { 十月: "10" },
    { 十一月: "11" },
    { 十二月: "12" },
  ];

  // 星期名称数组
  const weekdayNames = ["日", "一", "二", "三", "四", "五", "六"];

  // 调整星期名称顺序，使第一个显示的是指定的第一天
  const orderedWeekdayNames = [...weekdayNames.slice(firstDayOfWeek), ...weekdayNames.slice(0, firstDayOfWeek)];

  // 检查是否为闰年
  const isLeapYear = year => {
    return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
  };

  // 获取某月的天数
  const getDaysInMonth = (year, month) => {
    const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    // 处理闰年的二月
    if (month === 1 && isLeapYear(year)) {
      return 29;
    }

    return daysInMonth[month];
  };

  // 生成日历网格数据
  const generateCalendar = (year, month) => {
    const calendarData = [];

    // 获取当月第一天是星期几（0-6，0是星期日）
    const firstDayOfMonth = new Date(year, month, 1).getDay();

    // 调整为以指定的第一天开始的索引
    const adjustedFirstDay = (firstDayOfMonth - firstDayOfWeek + 7) % 7;

    // 获取上个月的天数
    const prevMonth = month === 0 ? 11 : month - 1;
    const prevYear = month === 0 ? year - 1 : year;
    const daysInPrevMonth = getDaysInMonth(prevYear, prevMonth);

    // 获取当月的天数
    const daysInCurrentMonth = getDaysInMonth(year, month);

    // 填充上个月的日期
    if (showOtherMonths) {
      for (let i = adjustedFirstDay - 1; i >= 0; i--) {
        const day = daysInPrevMonth - i;
        const date = new Date(prevYear, prevMonth, day);
        calendarData.push({
          date,
          day,
          month: prevMonth,
          year: prevYear,
          isCurrentMonth: false,
        });
      }
    }

    // 填充当月的日期
    for (let i = 1; i <= daysInCurrentMonth; i++) {
      const date = new Date(year, month, i);
      calendarData.push({
        date,
        day: i,
        month: month,
        year: year,
        isCurrentMonth: true,
      });
    }

    // 计算需要多少个下个月的日期来填满最后一行
    const daysInCalendar = calendarData.length;
    const daysToAdd = 7 - (daysInCalendar % 7);

    // 填充下个月的日期，使日历总共有完整的行
    if (showOtherMonths && daysToAdd < 7) {
      const nextMonth = month === 11 ? 0 : month + 1;
      const nextYear = month === 11 ? year + 1 : year;

      for (let i = 1; i <= daysToAdd; i++) {
        const date = new Date(nextYear, nextMonth, i);
        calendarData.push({
          date,
          day: i,
          month: nextMonth,
          year: nextYear,
          isCurrentMonth: false,
        });
      }
    }

    return calendarData;
  };

  // 切换到上个月
  const goToPrevMonth = () => {
    setCurrentDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setMonth(newDate.getMonth() - 1);
      return newDate;
    });
  };

  // 切换到下个月
  const goToNextMonth = () => {
    setCurrentDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setMonth(newDate.getMonth() + 1);
      return newDate;
    });
  };

  // 切换到上一年
  const goToPrevYear = () => {
    setCurrentDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setFullYear(newDate.getFullYear() - 1);
      return newDate;
    });
  };

  // 切换到下一年
  const goToNextYear = () => {
    setCurrentDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setFullYear(newDate.getFullYear() + 1);
      return newDate;
    });
  };

  // 回到今天
  const goToToday = () => {
    setCurrentDate(new Date());
  };

  // // 切换视图模式
  // const togglemode = () => {
  //   setmode((prevMode) => (prevMode === "month" ? "year" : "month"));
  // };

  // 检查是否为今天
  const isToday = date => {
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  // 检查是否为当前选中的日期
  const isSelected = date => {
    return (
      date.getDate() === currentDate.getDate() &&
      date.getMonth() === currentDate.getMonth() &&
      date.getFullYear() === currentDate.getFullYear()
    );
  };

  // 检查是否为当前月份
  const isCurrentMonth = monthIndex => {
    return monthIndex === today.getMonth() && currentYear === today.getFullYear();
  };

  // 过滤某天的事件
  const getEventsForDate = date => {
    // 确保events是数组类型
    if (!Array.isArray(events)) {
      console.warn("Events prop is not an array:", events);
      return [];
    }

    return events.filter(event => {
      // 简单的日期比较，实际应用中可能需要更复杂的逻辑
      return (
        event.date.getDate() === date.getDate() &&
        event.date.getMonth() === date.getMonth() &&
        event.date.getFullYear() === date.getFullYear()
      );
    });
  };

  // 获取某月的事件数量
  const getEventsCountForMonth = (year, month) => {
    // 确保events是数组类型
    if (!Array.isArray(events)) {
      console.warn("Events prop is not an array:", events);
      return 0;
    }

    return events.filter(event => {
      return event.date.getMonth() === month && event.date.getFullYear() === year;
    }).length;
  };

  // 过滤某月的事件
  const getEventsForMonth = (year, month) => {
    // 确保events是数组类型
    if (!Array.isArray(events)) {
      console.warn("Events prop is not an array:", events);
      return [];
    }

    return events.filter(event => {
      return event.date.getMonth() === month && event.date.getFullYear() === year;
    });
  };

  // 生成日历网格
  const calendarGrid = generateCalendar(currentYear, currentMonth);

  // 保持今天的日期更新
  useEffect(() => {
    // const timer = setInterval(() => {
    //   setToday(new Date());
    // }, 60000); // 每分钟更新一次
    // return () => clearInterval(timer);
  }, []);

  // 渲染月视图
  const renderMonthView = () => {
    return (
      <>
        <div className="weekdays-header">
          {orderedWeekdayNames.map((weekday, index) => (
            <div key={index} className="weekday-cell">
              <div className="weekday-title">{renderWeekdayHeader(`周${weekday}`)}</div>
              <div className="weekday-extra">{renderWeekdayHeaderExtra(weekday)}</div>
            </div>
          ))}
        </div>

        <div className="calendar-grid">
          {calendarGrid.map((dayObj, index) => {
            const dayEvents = getEventsForDate(dayObj.date);
            const isTodayDay = isToday(dayObj.date);

            return (
              <div
                key={index}
                className={`day-cell ${dayObj.isCurrentMonth ? "current-month" : "other-month"} ${
                  isTodayDay ? "today" : ""
                }`}
              >
                <div className="day-header">
                  <span className={`day-number ${isTodayDay ? "today-number" : ""}`}>{dayObj.day}</span>
                </div>

                <div className="day-content">{renderDayContent(dayObj.date, dayEvents)}</div>
              </div>
            );
          })}
        </div>
      </>
    );
  };

  // 渲染年视图
  const renderYearView = () => {
    return (
      <div className="year-view-container">
        <div className="year-grid">
          {monthNames.map((monthName, monthIndex) => {
            const eventsCount = getEventsCountForMonth(currentYear, monthIndex);
            const isTodayMonth = isCurrentMonth(monthIndex);
            const monthEvents = getEventsForMonth(currentYear, monthIndex);
            const monthLabel = Object.keys(monthName)[0];
            const monthValue = Object.values(monthName)[0];

            return (
              <div
                key={monthIndex}
                className={`month-cell${isTodayMonth ? " today-month" : ""}`}
                onClick={() => setCurrentDate(new Date(currentYear, monthIndex, 1))}
              >
                <div className="month-header">
                  <div className="month-title">{renderMonthHeader(monthLabel, currentYear)}</div>
                  <div className="month-extra">{renderMonthHeaderExtra(monthIndex, currentYear)}</div>
                </div>
                <div className="month-content">
                  {renderMonthContent(monthValue, currentYear, monthEvents)}
                  {eventsCount > 0 && <div className="event-count">{eventsCount} 个事件</div>}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // 渲染当前视图
  const renderCurrentView = () => {
    if (mode === "year") {
      return renderYearView();
    }
    return renderMonthView();
  };

  return (
    <div className="outlook-calendar">
      {/* 日历内容 */}
      <div className="calendar-content">{renderCurrentView()}</div>
    </div>
  );
};

export default OutlookCalendar;

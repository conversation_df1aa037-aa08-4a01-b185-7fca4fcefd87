#root {
    padding: 6px;
}
.app-container {
    margin       : 0 auto;
    min-width    : 1450px;
    background   : #fff;
    border-radius: 10px;
    box-shadow   : 0 2px 16px rgba(0, 0, 0, 0.06);
    padding      : 16px;
    margin-top   : 12px;

    .no-task {
        color     : #bbb;
        font-size : 14px;
        text-align: center;
        padding   : 10px 0;
    }
    .ant-spin-nested-loading{
        width: 100%;
    }
    .weekday-short {
        font-size  : 14px;
        color      : #222;
        font-weight: 600;
    }

    .status-indicators {
        display        : flex;
        flex-wrap      : wrap;
        font-weight    : normal;
        justify-content: flex-end;

        .indicator {
            font-size : 14px;
            width     : 65px;
            text-align: center;
        }
    }

    .custom-month-header {
        display        : flex;
        align-items    : center;
        justify-content: center;
        font-size      : 15px;
        font-weight    : 600;
        color          : #222;

        .month-text {
            margin-left: 2px;
        }
    }

    .month-icon {
        display        : flex;
        align-items    : center;
        justify-content: center;
    }

    .event-details {
        margin-top   : 24px;
        background   : #fff;
        border-radius: 8px;
        box-shadow   : 0 2px 8px rgba(0, 0, 0, 0.06);
        padding      : 16px 20px;
        max-width    : 420px;

        h3 {
            margin-top: 0;
        }

        button {
            margin-top   : 12px;
            padding      : 4px 18px;
            border-radius: 6px;
            border       : none;
            background   : #3399ff;
            color        : #fff;
            font-size    : 14px;
            cursor       : pointer;
        }
    }

    .tab-btn {
        background   : #f5f5f5;
        border       : 1px solid #ddd;
        border-radius: 4px 4px 0 0;
        padding      : 8px 24px;
        font-size    : 16px;
        cursor       : pointer;
        margin-right : 2px;
        color        : #333;
        outline      : none;
        transition   : background 0.2s, color 0.2s;
    }

    .tab-btn.active {
        background   : #746a95;
        color        : #fff;
        border-bottom: 1px solid #fff;
        z-index      : 2;
        position     : relative;
    }
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #746A95 !important;
}
.ant-tabs-ink-bar {
  background: #746A95 !important;
}

.circle {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 4px;
  vertical-align: middle;
  margin-bottom: 2px; /* 微调圆点向上对齐 */
}

import React, { useEffect, useState } from "react";
import OutlookCalendar from "./Calendar";
import CalendarHeader from "./CalendarHeader";
import DayContentAll from "./DayContentAll";
import MonthContentAll from "./MonthContentAll";
import { UrlParams } from "./Filter";
import "./styles.css";
import moment from "moment";

const NoTask = () => <div className="no-task">无任务</div>;

const renderDayContent = (date, filter) => {
  return <DayContentAll date={date} filter={filter} />;
};

const renderWeekdayHeader = weekday => <div className="weekday-short">{weekday}</div>;

const renderWeekdayHeaderExtra = (weekday, filter) => {
  if (filter !== "clinical") {
    return (
      <div className="status-indicators">
        <span className="indicator completed">已完成</span>
        <span className="indicator planned">已计划</span>
      </div>
    );
  }
};

const renderMonthHeader = (month, year) => (
  <div className="custom-month-header">
    <span className="month-text">{month}</span>
  </div>
);

const renderMonthHeaderExtra = (monthIndex, year) => (
  <div className="status-indicators">
    <span className="indicator completed">已完成</span>
    <span className="indicator planned">已计划</span>
  </div>
);

const renderMonthContent = (monthIndex, year, events) => {
  return <MonthContentAll monthIndex={monthIndex} year={year} events={events} />;
};

const CalendarContainer = () => {
  const [currentDate, setCurrentDate] = useState(new Date(UrlParams.getCurrentDate()));
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [viewMode, setViewMode] = useState(UrlParams.getMode());
  const [selectedFilter, setSelectedFilter] = useState("all");
  useEffect(() => {
    UrlParams.setCurrentDate(moment(currentDate).format("YYYY-MM-DD"));
  }, [currentDate]);

  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  const toNextMonth = () => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + 1);
    setCurrentDate(newDate);
  };

  const toLastMonth = () => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() - 1);
    setCurrentDate(newDate);
  };

  const toNextYear = () => {
    const newDate = new Date(currentDate);
    newDate.setFullYear(newDate.getFullYear() + 1);
    setCurrentDate(newDate);
  };

  const toLastYear = () => {
    const newDate = new Date(currentDate);
    newDate.setFullYear(newDate.getFullYear() - 1);
    setCurrentDate(newDate);
  };

  const toThisMonth = () => setCurrentDate(new Date());

  const toggleMode = mode => {
    setViewMode(mode);
    UrlParams.setMode(mode);
    // 模式变化时重新计算日期范围
    UrlParams.setCurrentDate(moment(currentDate).format("YYYY-MM-DD"));
    console.log("视图模式已切换到:", mode);
  };

  const handleFilterChange = filter => {
    setSelectedFilter(filter);
    console.log("过滤器已更改为:", filter);
  };

  return (
    <div className="calendar-wrapper">
      <CalendarHeader
        year={currentYear}
        month={currentMonth}
        toNextMonth={toNextMonth}
        toLastMonth={toLastMonth}
        toNextYear={toNextYear}
        toLastYear={toLastYear}
        toThisMonth={toThisMonth}
        isToday={
          currentDate.getDate() === new Date().getDate() &&
          currentDate.getMonth() === new Date().getMonth() &&
          currentDate.getFullYear() === new Date().getFullYear()
        }
        currentMode={viewMode}
        toggleMode={toggleMode}
        selectedFilter={selectedFilter}
        onFilterChange={handleFilterChange}
      />
      <OutlookCalendar
        mode={viewMode}
        date={currentDate}
        renderDayContent={date => renderDayContent(date, selectedFilter)}
        renderWeekdayHeader={renderWeekdayHeader}
        renderWeekdayHeaderExtra={weekday => renderWeekdayHeaderExtra(weekday, selectedFilter)}
        renderMonthHeader={renderMonthHeader}
        renderMonthHeaderExtra={renderMonthHeaderExtra}
        renderMonthContent={renderMonthContent}
        firstDayOfWeek={1}
      />
      {selectedEvent && (
        <div className="event-details">
          <h3>事件详情</h3>
          <p>
            <strong>标题:</strong> {selectedEvent.title}
          </p>
          <p>
            <strong>日期:</strong> {selectedEvent.date.toLocaleDateString()}
          </p>
          <p>
            <strong>类型:</strong> {selectedEvent.type}
          </p>
          <button onClick={() => setSelectedEvent(null)}>关闭</button>
        </div>
      )}
    </div>
  );
};

export default CalendarContainer;

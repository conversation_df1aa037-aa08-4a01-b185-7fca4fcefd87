import React, { useState, useEffect } from "react";
import { Layout, Radio, Progress, Spin, Empty, ConfigProvider } from "antd";
import zhCN from "antd/es/locale/zh_CN";
import "./index.less";
import { GEHCLogo_h_s, CT_icon, cycle_bg, icon_5, icon_6, icon_7, icon_8, icon_9 } from "@/images/export";
import DataEchart from "./components/DataEchart.jsx";
import Rate from "./components/Rate.jsx";
import QCSystemCarousel from "./components/QCSystemCarousel.jsx";
import ResizeFontSize from "./components/ResizeFontSize.jsx";
import {
  getZkDashboardAssetCount,
  getZkPmFinishedCount,
  getZkOperationRate,
  getZkWorkOrderDataCount,
  getZkOrderDetail,
  getZkPmDetail,
  getZkWorkOrder,
} from "@/service";
import moment from "moment";
const { Header, Content } = Layout;
const filterTimeOptions = [
  { label: "近一年", value: "year" },
  { label: "近90天", value: "quarter" },
  { label: "近30天", value: "month" },
  { label: "近七天", value: "week" },
];

function RadQCBoard() {
  const { selectTimeScope, radioValue, filterTime, data, loading } = useRadQCBoard();
  const {
    zkDashboardAssetCount,
    zkPmFinishedCount,
    zkOperationRate,
    zkWorkOrderDataCount,
    zkOrderDetail,
    zkPmDetail,
    zkWorkOrder,
  } = data;
  return (
    <Layout className="RadQCBoard">
      <Header>
        <img className="ge-logo" src={GEHCLogo_h_s} alt="ge-logo" />
        <span className="header-title">
          <img className="CT-icon" src={CT_icon} alt="ge-logo" />
          放射科质控大屏
        </span>
      </Header>
      <ConfigProvider locale={zhCN}>
        <Content>
          <Spin tip="获取数据 Loading..." spinning={loading} size="large">
            <div className="time-interval">
              <Radio.Group
                buttonStyle="solid"
                options={filterTimeOptions}
                optionType="button"
                onChange={e => {
                  selectTimeScope(e.target.value);
                }}
                value={radioValue}
              />
            </div>
            <div className="year-QC-board">
              <h3 className="year-QC-board-title">质控概览</h3>
              <div className="QC-chart">
                <div className="QC-chart-item device-num">
                  {zkDashboardAssetCount ? (
                    <>
                      <h4>质控设备数量</h4>
                      <div className="device-cont">
                        <div className="device-num-wrap">
                          <ResizeFontSize baseFontSize={70}>{zkDashboardAssetCount.assetTotalCount}</ResizeFontSize>
                          <span>台</span>
                        </div>
                        <div className="device-echart">
                          <DataEchart name={"device-num"} data={zkDashboardAssetCount.assetGroupCount}></DataEchart>
                        </div>
                      </div>
                    </>
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_DEFAULT} description={"暂无数据"} />
                  )}
                </div>

                <div className="QC-chart-item order-num">
                  {zkPmFinishedCount ? (
                    <>
                      <h4>质控完成工单量</h4>
                      <div className="order-cont">
                        <div className="order-num-wrap">
                          <ResizeFontSize baseFontSize={70}>{zkPmFinishedCount.workOrderTotalCount}</ResizeFontSize>
                          <span>单</span>
                        </div>
                        <div className="order-echart">
                          <DataEchart name={"order-num"} data={zkPmFinishedCount.workOrderTypeCount}></DataEchart>
                        </div>
                      </div>
                    </>
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_DEFAULT} description={"暂无数据"} />
                  )}
                </div>

                <div className="QC-chart-item pass-rate">
                  <h4>强检合格率</h4>

                  <Progress
                    format={value => (
                      <div className="text-wrap">
                        <div>{value}</div>
                        <div className="percent-sign">%</div>
                      </div>
                    )}
                    strokeColor="#8FBF9D"
                    strokeWidth="5"
                    trailColor="rgba(249, 249, 250, 0.06)"
                    type="circle"
                    percent={100}
                  />
                </div>

                <div className="QC-chart-item startup-rate">
                  {zkOperationRate ? (
                    <>
                      <h4>开机率 (%)</h4>
                      <div className="echart-wrap">
                        <Rate value={zkOperationRate.operationRate} />
                      </div>
                    </>
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_DEFAULT} description={"暂无数据"} />
                  )}
                </div>
              </div>
            </div>
            <div className="QC-data-section">
              <div className="QC-data-loop">
                {zkWorkOrderDataCount ? (
                  <>
                    <h3 className="QC-data-title">质控闭环数据（预防维护工单维度）</h3>
                    <div className="QC-data-content">
                      <div className="QC-data-echart">
                        <DataEchart
                          name={"qc-order"}
                          data={[
                            {
                              name: "质控完成率",
                              numerical: zkWorkOrderDataCount.zkFinishedRate,
                            },
                            {
                              name: "初检合格率",
                              numerical: zkWorkOrderDataCount.firstCheckPassRate,
                            },
                            { name: "复检合格率", numerical: zkWorkOrderDataCount.reCheckPassRate },
                          ]}
                        ></DataEchart>
                      </div>
                      <div className="QC-data-progress">
                        <div className="progress-item">
                          <div className="item-content">
                            <h5>初检合格量</h5>
                            <Progress
                              percent={100}
                              strokeColor={{
                                from: "#7245D9",
                                to: "#9670EE",
                              }}
                              strokeWidth="8px"
                              trailColor="rgba(249, 249, 250, 0.08)"
                              format={value => {
                                return zkWorkOrderDataCount.firstCheckPassCount;
                              }}
                            />
                          </div>
                          <div className="item-content">
                            <h5>现场维修量</h5>
                            <Progress
                              percent={100}
                              strokeColor={{
                                from: "#00C2EB",
                                to: "#00C2EB",
                              }}
                              strokeWidth="8px"
                              trailColor="rgba(249, 249, 250, 0.08)"
                              format={value => {
                                return zkWorkOrderDataCount.woCount;
                              }}
                            />
                          </div>
                          <div className="item-content">
                            <h5>复检合格量/不合格量</h5>
                            <Progress
                              percent={100}
                              strokeColor={{
                                from: "#EDC50C",
                                to: "#EDC50C",
                              }}
                              strokeWidth="8px"
                              trailColor="rgba(249, 249, 250, 0.08)"
                              format={() =>
                                `${zkWorkOrderDataCount.reCheckPassCount || 0}/${
                                  zkWorkOrderDataCount.reCheckNotPassCount || 0
                                }`
                              }
                            />
                          </div>
                          <div className="item-content">
                            <h5>设备报废量</h5>
                            <Progress
                              percent={100}
                              strokeColor={{
                                from: "#B6BECC",
                                to: "#B6BECC",
                              }}
                              strokeWidth="8px"
                              trailColor="rgba(249, 249, 250, 0.08)"
                              format={value => {
                                return zkWorkOrderDataCount.terminatedCount;
                              }}
                            />
                          </div>
                        </div>
                        <img className="QC-data-cycle" src={cycle_bg} />
                      </div>
                    </div>
                  </>
                ) : (
                  <Empty image={Empty.PRESENTED_IMAGE_DEFAULT} description={"暂无数据"} />
                )}
              </div>

              <div className="QC-system-section">
                <h3 className="QC-system-title">医疗设备质量控制体系</h3>
                <QCSystemCarousel data={{ zkOrderDetail, zkPmDetail, zkWorkOrder }} />
              </div>
              <div className="QC-empty-section">
                <p>
                  依据《放射科管理规范与质控标准》（2017版）、《放射科治疗质量控制基本指南》、《放射科诊疗管理规定》，医疗设备质量控制管理目标：
                </p>
                <div className="item-ad">
                  <div className="item-icon">
                    <img src={icon_5} />
                  </div>
                  <span>确保设备安全与稳定</span>
                </div>
                <div className="item-ad">
                  <div className="item-icon">
                    <img src={icon_6} />
                  </div>

                  <span>提高影像质量与诊断准确性</span>
                </div>
                <div className="item-ad">
                  <div className="item-icon">
                    <img src={icon_7} />
                  </div>
                  <span>提供工作效率与服务质量</span>
                </div>
                <div className="item-ad">
                  <div className="item-icon">
                    <img src={icon_8} />
                  </div>
                  <span>持续改进与技术创新</span>
                </div>
                <div className="item-ad">
                  <div className="item-icon">
                    <img src={icon_9} />
                  </div>
                  <span>遵循法规和标准</span>
                </div>
              </div>
            </div>
          </Spin>
        </Content>
      </ConfigProvider>
    </Layout>
  );
}

const useRadQCBoard = () => {
  const [radioValue, setRadioValue] = useState("year");
  const [filterTime, setFilterTime] = useState([moment().startOf("year"), moment()]);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({
    zkDashboardAssetCount: null, //质控设备数量
    zkPmFinishedCount: null, //质控完成工单量
    zkOperationRate: null, //开机率
    zkWorkOrderDataCount: null, //质控闭环数据（预防维护工单维度）
    zkOrderDetail: null, //日常质控
    zkPmDetail: null, //预防维护
    zkWorkOrder: null, //质控维修,
  });
  const selectTimeScope = timeScope => {
    let startTime, endTime;
    setRadioValue(timeScope);
    switch (timeScope) {
      case "week":
        startTime = moment().subtract(6, "days");
        endTime = moment().endOf("day");
        break;
      case "month":
        startTime = moment().subtract(29, "days");
        endTime = moment().endOf("day");
        break;
      case "quarter":
        startTime = moment().subtract(89, "days");
        endTime = moment().endOf("day");

        break;
      case "year":
        startTime = moment().startOf("year");
        endTime = moment().endOf("day");

        break;
      default:
        break;
    }
    setFilterTime([startTime, endTime]);
  };

  const getDashboardData = async date => {
    setLoading(true);
    try {
      const [
        zkDashboardAssetCount,
        zkPmFinishedCount,
        zkOperationRate,
        zkWorkOrderDataCount,
        zkOrderDetail,
        zkPmDetail,
        zkWorkOrder,
      ] = await Promise.all([
        getZkDashboardAssetCount(date),
        getZkPmFinishedCount(date),
        getZkOperationRate(date),
        getZkWorkOrderDataCount(date),
        getZkOrderDetail(date),
        getZkPmDetail(date),
        getZkWorkOrder(date),
      ]);
      setData(data => ({
        zkDashboardAssetCount,
        zkPmFinishedCount,
        zkOperationRate,
        zkWorkOrderDataCount,
        zkOrderDetail,
        zkPmDetail,
        zkWorkOrder,
      }));
    } catch (error) {
      console.error("Failed to fetch dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  //页面窗口缩放
  useEffect(() => {
    const baseWidth = 1920;
    const baseHeight = 1077;
    const scalePage = () => {
      let scale = 1;
      if (window.innerWidth != baseWidth || window.innerHeight !== baseHeight) {
        scale = Math.min(window.innerWidth / baseWidth, window.innerHeight / baseHeight);
      }
      const root = document.querySelector(".RadQCBoard");
      if (root) {
        // x 轴居中，y 轴到顶
        root.style.position = "absolute";
        root.style.top = "0";
        root.style.left = "50%";
        root.style.transform = `translateX(-50%) scale(${scale})`;
        root.style.transformOrigin = "top center";
        const rootContainer = document.querySelector("#root");
        if (rootContainer) {
          rootContainer.style.backgroundColor = "#191919";
        }
      }
    };
    scalePage();
    window.addEventListener("resize", scalePage);
    return () => {
      window.removeEventListener("resize", scalePage);
    };
  }, []);

  useEffect(() => {
    //filterTime 变化调用接口查询数据
    console.log(
      "filterTime0==",
      moment(filterTime[0]).format("YYYY-MM-DD"),
      "filterTime1==",
      moment(filterTime[1]).format("YYYY-MM-DD"),
    );
    getDashboardData({
      from: moment(filterTime[0]).format("YYYY-MM-DD"),
      to: moment(filterTime[1]).format("YYYY-MM-DD"),
    });
  }, [filterTime]);

  return {
    selectTimeScope,
    radioValue,
    filterTime,
    data,
    loading,
  };
};

export default RadQCBoard;

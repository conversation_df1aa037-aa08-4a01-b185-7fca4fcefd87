import React, { useState, useEffect, useRef, useMemo } from "react";
import { icon_1, icon_2, icon_3, icon_4 } from "@/images/export";
import DataEchart from "./DataEchart.jsx";
import moment from "moment";
import { Table } from "antd";
import "./index.less";

const QCSystemCarousel = props => {
  const [currentState, setCurrentState] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const interval = useRef();
  const { data } = props;
  const { zkOrderDetail, zkPmDetail, zkWorkOrder } = data;
  const zkPmDetailColumns = [
    {
      title: "",
      dataIndex: "ruleLevel",
      key: "ruleLevel",
    },
    {
      title: "已计划",
      dataIndex: "planedCount",
      key: "planedCount",
    },
    {
      title: "已完成",
      dataIndex: "finishCount",
      key: "finishCount",
    },
    {
      title: "初检合格率",
      dataIndex: "firstCheckPassRate",
      key: "firstCheckPassRate",
      render: item => {
        return <span className="status-dot-container">{`${item || 0}%`}</span>;
      },
    },
    {
      title: "按时完成率",
      dataIndex: "finishedOnTimeRate",
      key: "finishedOnTimeRate",
      render: item => {
        return <span className="status-dot-container">{`${item || 0}%`}</span>;
      },
    },
    {
      title: "复检合格率",
      dataIndex: "reCheckPassRate",
      key: "reCheckPassRate",
      render: item => {
        return <span className="status-dot-container">{`${item || 0}%`}</span>;
      },
    },
    {
      title: "整体完成率",
      dataIndex: "totalFinishRate",
      key: "totalFinishRate",
      render: item => {
        return <span className="status-dot-container">{`${item || 0}%`}</span>;
      },
    },
  ];
  const zkOrderDetailColumns = [
    {
      title: "设备名称",
      dataIndex: "assetName",
      key: "assetName",
      width: 100,
    },
    {
      title: "所属科室",
      dataIndex: "clinicalDeptName",
      key: "clinicalDeptName",
    },
    {
      title: "工单类型",
      dataIndex: "woType",
      key: "woType",
      width: 80,
    },
    {
      title: "实际完成日期",
      dataIndex: "finishedDate",
      key: "finishedDate",
      render: item => {
        return <span className="status-dot-container">{moment(item).format("YYYY-MM-DD")}</span>;
      },
    },

    {
      title: "质控结果",
      dataIndex: "zkResult",
      key: "zkResult",
      render: item => {
        return (
          <div className="status-dot-container">
            <span className={`status-dot ${item === "合格" ? "green" : "red"}`}></span>
            {item}
          </div>
        );
      },
    },
    {
      title: "计划日期",
      dataIndex: "planTime",
      key: "planTime",
      render: item => {
        return <span className="status-dot-container">{moment(item).format("YYYY-MM-DD")}</span>;
      },
    },
  ];
  //故障类分布数据转化
  const toRepairData = arr => {
    if (!arr || arr.length == 0) {
      return [];
    }
    return arr.map(item => {
      // 提取括号内容作为 subtitle
      const match = item.caseType.match(/^([^\(]+)(?:\(([^)]*)\))?$/);
      return {
        title: match ? match[1].trim() : item.caseType,
        subtitle: match && match[2] ? `（${match[2]}）` : "",
        value: item.count,
      };
    });
  };
  const creatHighFaultAssetTopCount = arr => {
    if (!arr || arr.length == 0) {
      return [];
    }
    return arr.map(item => {
      return {
        title: `${item.localName||''}${item.clinicalDeptName||''}${item.name||''}`,
        value: item.count,
      };
    });
  };

  // 三种状态的数据配置
  const statesData = useMemo(
    () => [
      {
        title: "医疗设备质量控制体系",
        subtitle: "临床 | 临床巡检",
        type: "zkOrderDetail",
        contentData: zkOrderDetail,
      },
      {
        title: "质控工单明细",
        subtitle: "医工 | 预防维护",
        type: "zkPmDetail",
        contentData: zkPmDetail,
      },
      {
        title: "医疗设备质量控制体系",
        subtitle: "医工 | 质控维修",
        type: "zkWorkOrder.highFaultAssetTopCount",
        contentData: [
          { name: "报修", count: zkWorkOrder?.workOrderCount, unit: "单" },
          { name: "维修", count: zkWorkOrder?.repairingWorkOrder, unit: "单" },
        ],
        repairData: zkWorkOrder && creatHighFaultAssetTopCount(zkWorkOrder.highFaultAssetTopCount),
      },
      {
        title: "医疗设备质量控制体系",
        subtitle: "专业机构 | 医学计量",
        type: "zkWorkOrder.caseTypeCount",
        contentData: [
          { name: "报修", count: zkWorkOrder?.workOrderCount, unit: "单" },
          { name: "维修", count: zkWorkOrder?.repairingWorkOrder, unit: "单" },
        ],
        repairData: zkWorkOrder && toRepairData(zkWorkOrder.caseTypeCount),
      },
    ],
    [zkOrderDetail, zkPmDetail, zkWorkOrder],
  );

  const currentData = statesData[currentState];
  // 自动轮播逻辑
    useEffect(() => {
      if (!isPaused) {
        interval.current = setInterval(() => {
          setCurrentState(prev => (prev + 1) % statesData.length);
          setProgress(0); // 重置进度条
        }, 5000);
      } else {
        clearInterval(interval.current);
      }

      return () => clearInterval(interval.current);
    }, [statesData.length, isPaused]);

  return (
    <div className="qc-system-carousel">
      {/* 轮播内容 */}
      <div className="carousel-content">
        {/* 上半部分 - 进度展示 */}
        <div
          className="carousel-header"
          // onMouseEnter={() => setIsPaused(true)} // 鼠标移入时暂停
          onMouseLeave={() => setIsPaused(false)} // 鼠标移出时继续
        >
          <div className="header-icons">
            <div className="item-wrap-first">
              <div className="icon-item">
                <img src={icon_1} alt={`icon-1`} />
              </div>
              <div className="icon-item">
                <img src={icon_2} alt={`icon-2`} />
              </div>
              <div className="icon-item">
                <img src={icon_3} alt={`icon-3`} />
              </div>
            </div>
            <div className="item-wrap-last">
              <div className="icon-item">
                <img src={icon_4} alt={`icon-4`} />
              </div>
            </div>
          </div>
          {/* 进度指示器 */}
          <div className="carousel-indicators">
            {statesData.map((item, index) => (
              <div
                key={index}
                className={`indicator ${index <= currentState || index == 3 ? "active" : ""}`}
                onClick={() => {
                  if (index == currentState || index == 3) {
                    return;
                  }
                  setCurrentState(index);
                  setProgress(0);

                  setIsPaused(true); // 点击时暂停定时器
                }}
              >
                <div className="indicator-number">{index + 1}</div>

                <span>{item.subtitle}</span>
              </div>
            ))}
            <div className="indicator-progress">
              {statesData.slice(0, statesData.length - 2).map((_, index) => (
                <div key={index} className="progress-bar-wrap">
                  {index <= currentState && (
                    <div
                      className="progress-bar"
                      style={{
                        width: index >= currentState ? `${progress}%` : "100%",
                      }}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 下半部分 - 内容变化 */}
        <div className="carousel-body">
          {currentState === 0 && (
            <div key={`daily-0`} className="state-content state-monthly">
              <div className="content-table">
                <Table
                  rowKey={(record, index) => {
                    return index;
                  }}
                  scroll={{ x: 765, y: 240 }}
                  columns={zkOrderDetailColumns}
                  dataSource={currentData.contentData}
                  pagination={false}
                />
              </div>
            </div>
          )}

          {currentState === 1 && (
            <div key={`monthly-1`} className="state-content state-monthly">
              <div className="monthly-header">
                <span className="section-title">{currentData.title}</span>
              </div>
              <div className="monthly-table">
                <Table
                  rowKey={(record, index) => {
                    return index;
                  }}
                  scroll={{ x: 720, y: 212 }}
                  columns={zkPmDetailColumns}
                  dataSource={currentData.contentData}
                  pagination={false}
                />
              </div>
            </div>
          )}

          {currentState === 2 && (
            <div key={`repair-2`} className="state-content state-repair">
              <div className="repair-stats">
                {currentData.contentData.map((item, index) => (
                  <div key={index} className="stat-circle">
                    <div className="stat-label">{item.name}</div>
                    <div className="circle-content">
                      <div className="stat-number">{item.count}</div>
                      <div className="stat-unit">{item.unit}</div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="repair-progress">
                <div className="progress-header">
                  <span>高故障设备排名</span>
                </div>
                <div className="progress-content">
                  <DataEchart name={"equipment-failure"} data={currentData.repairData}></DataEchart>
                </div>
              </div>
            </div>
          )}
          {currentState === 3 && (
            <div key="daily-repair-stats" className="state-content state-daily">
              <div className="repair-stats">
                {currentData.contentData.map((item, index) => (
                  <div key={index} className="stat-circle">
                    <div className="stat-label">{item.name}</div>
                    <div className="circle-content">
                      <div className="stat-number">{item.count}</div>
                      <div className="stat-unit">{item.unit}</div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="repair-progress">
                <div className="progress-header">
                  <span>故障类别分布</span>
                </div>
                <div className="progress-content">
                  <DataEchart name={"distribution-fault"} data={currentData.repairData}></DataEchart>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QCSystemCarousel;
